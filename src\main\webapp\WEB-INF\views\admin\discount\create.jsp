<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <title>Thêm Mã Giảm Giá</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="/css/style.css">
    <style>
        .discount-type-info {
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 5px 5px 0;
        }
        .field-group {
            background-color: #ffffff;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .field-group h6 {
            color: #495057;
            font-weight: 600;
            margin-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 8px;
        }
        .form-control:focus, .form-select:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        .required-field {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-2 p-0">
            <jsp:include page="../layout/sidebar.jsp" />
        </div>

        <!-- Main Content -->
        <div class="col-10">
            <jsp:include page="../layout/header.jsp"/>
            <div class="p-4">
                <jsp:include page="../layout/page-title.jsp">
                    <jsp:param name="title" value="Thêm Mã Giảm Giá"/>
                    <jsp:param name="icon" value="fa-solid fa-plus"/>
                </jsp:include>

                <!-- Breadcrumb -->
                <nav aria-label="breadcrumb" class="mb-4">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="/admin/Discount">Quản lý mã giảm giá</a></li>
                        <li class="breadcrumb-item active">Thêm mới</li>
                    </ol>
                </nav>

                <!-- Thông báo -->
                <c:if test="${not empty message}">
                    <div class="alert ${messageType == 'success' ? 'alert-success' : 'alert-danger'} alert-dismissible fade show" role="alert">
                        ${message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                </c:if>

                <!-- Hướng dẫn sử dụng -->
                <div class="discount-type-info">
                    <h6><i class="fas fa-info-circle me-2"></i>Hướng dẫn tạo mã giảm giá</h6>
                    <div class="row">
                        <div class="col-md-4">
                            <strong>Loại 1 - Giảm theo phần trăm:</strong>
                            <p class="mb-0 small">Nhập % giảm và có thể giới hạn số tiền giảm tối đa</p>
                        </div>
                        <div class="col-md-4">
                            <strong>Loại 2 - Giảm số tiền cố định:</strong>
                            <p class="mb-0 small">Nhập số tiền giảm cố định (VNĐ)</p>
                        </div>
                        <div class="col-md-4">
                            <strong>Loại 3 - Giảm phần trăm đặc biệt:</strong>
                            <p class="mb-0 small">Tương tự loại 1 nhưng có logic xử lý khác</p>
                        </div>
                    </div>
                </div>

                <!-- Form thêm mã giảm giá -->
                <form method="post" action="/admin/Discount/add" id="discountForm">
                    <div class="row">
                        <!-- Cột trái - Thông tin cơ bản -->
                        <div class="col-md-6">
                            <div class="field-group">
                                <h6><i class="fas fa-info-circle me-2"></i>Thông tin cơ bản</h6>

                                <div class="mb-3">
                                    <label for="code" class="form-label">
                                        <i class="fas fa-tag me-1"></i>Mã giảm giá <span class="required-field">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="code" name="code"
                                           placeholder="Nhập mã giảm giá (VD: SALE20, FREESHIP50)"
                                           required maxlength="50">
                                    <div class="form-text">Mã phải là duy nhất, không trùng lặp và không chứa ký tự đặc biệt</div>
                                </div>

                                <div class="mb-3">
                                    <label for="detail" class="form-label">
                                        <i class="fas fa-align-left me-1"></i>Mô tả chi tiết
                                    </label>
                                    <textarea class="form-control" id="detail" name="detail" rows="3"
                                              placeholder="Mô tả về mã giảm giá này (VD: Giảm 20% cho đơn hàng từ 500k)"
                                              maxlength="500"></textarea>
                                    <div class="form-text">Mô tả sẽ hiển thị cho khách hàng khi chọn mã giảm giá</div>
                                </div>

                                <div class="mb-3">
                                    <label for="type" class="form-label">
                                        <i class="fas fa-tags me-1"></i>Loại giảm giá <span class="required-field">*</span>
                                    </label>
                                    <select class="form-select" id="type" name="type" required>
                                        <option value="">-- Chọn loại giảm giá --</option>
                                        <option value="1">Loại 1: Giảm theo phần trăm</option>
                                        <option value="2">Loại 2: Giảm số tiền cố định</option>
                                        <option value="3">Loại 3: Giảm phần trăm đặc biệt</option>
                                    </select>
                                    <div class="form-text">Chọn loại giảm giá phù hợp với chiến lược kinh doanh</div>
                                </div>
                            </div>

                            <div class="field-group">
                                <h6><i class="fas fa-calendar me-2"></i>Thời gian hiệu lực</h6>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="startDate" class="form-label">
                                                <i class="fas fa-calendar-alt me-1"></i>Ngày bắt đầu <span class="required-field">*</span>
                                            </label>
                                            <input type="datetime-local" class="form-control" id="startDate" name="startDate" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="endDate" class="form-label">
                                                <i class="fas fa-calendar-times me-1"></i>Ngày kết thúc <span class="required-field">*</span>
                                            </label>
                                            <input type="datetime-local" class="form-control" id="endDate" name="endDate" required>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Cột phải - Giá trị giảm giá và điều kiện -->
                        <div class="col-md-6">
                            <div class="field-group">
                                <h6><i class="fas fa-money-bill-wave me-2"></i>Giá trị giảm giá</h6>

                                <div id="percentageGroup" class="mb-3" style="display: none;">
                                    <label for="percentage" class="form-label">
                                        <i class="fas fa-percent me-1"></i>Phần trăm giảm (%)
                                    </label>
                                    <input type="number" class="form-control" id="percentage" name="percentage"
                                           min="1" max="100" placeholder="Nhập % giảm (1-100)">
                                    <div class="form-text">Áp dụng cho loại giảm theo phần trăm</div>
                                </div>

                                <div id="amountGroup" class="mb-3" style="display: none;">
                                    <label for="amount" class="form-label">
                                        <i class="fas fa-money-bill me-1"></i>Số tiền giảm (VNĐ)
                                    </label>
                                    <input type="number" class="form-control" id="amount" name="amount"
                                           min="1000" step="1000" placeholder="Nhập số tiền giảm">
                                    <div class="form-text">Áp dụng cho loại giảm số tiền cố định</div>
                                </div>

                                <div id="maximumAmountGroup" class="mb-3" style="display: none;">
                                    <label for="maximumAmount" class="form-label">
                                        <i class="fas fa-coins me-1"></i>Số tiền giảm tối đa (VNĐ)
                                    </label>
                                    <input type="number" class="form-control" id="maximumAmount" name="maximumAmount"
                                           min="1000" step="1000" placeholder="Giới hạn số tiền giảm tối đa">
                                    <div class="form-text">Áp dụng khi dùng phần trăm giảm để tránh giảm quá nhiều</div>
                                </div>
                            </div>

                            <div class="field-group">
                                <h6><i class="fas fa-cogs me-2"></i>Điều kiện áp dụng</h6>

                                <div class="mb-3">
                                    <label for="minimumAmountInCart" class="form-label">
                                        <i class="fas fa-shopping-cart me-1"></i>Giá trị đơn hàng tối thiểu (VNĐ)
                                    </label>
                                    <input type="number" class="form-control" id="minimumAmountInCart" name="minimumAmountInCart"
                                           min="0" step="1000" placeholder="0 (không giới hạn)">
                                    <div class="form-text">Đơn hàng phải đạt giá trị này mới áp dụng được mã</div>
                                </div>

                                <div class="mb-3">
                                    <label for="maximumUsage" class="form-label">
                                        <i class="fas fa-users me-1"></i>Số lần sử dụng tối đa
                                    </label>
                                    <input type="number" class="form-control" id="maximumUsage" name="maximumUsage"
                                           min="1" placeholder="Để trống nếu không giới hạn">
                                    <div class="form-text">Giới hạn tổng số lần mã có thể được sử dụng</div>
                                </div>

                                <div class="mb-3">
                                    <label for="status" class="form-label">
                                        <i class="fas fa-toggle-on me-1"></i>Trạng thái <span class="required-field">*</span>
                                    </label>
                                    <select class="form-select" id="status" name="status" required>
                                        <option value="1" selected>Kích hoạt</option>
                                        <option value="0">Tạm dừng</option>
                                    </select>
                                    <div class="form-text">Chỉ mã đang kích hoạt mới có thể sử dụng</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Buttons -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body">
                                    <div class="d-flex gap-3 justify-content-center">
                                        <button type="submit" class="btn btn-success btn-lg">
                                            <i class="fas fa-save me-2"></i>Lưu mã giảm giá
                                        </button>
                                        <a href="/admin/Discount" class="btn btn-secondary btn-lg">
                                            <i class="fas fa-arrow-left me-2"></i>Quay lại danh sách
                                        </a>
                                        <button type="reset" class="btn btn-outline-warning btn-lg">
                                            <i class="fas fa-undo me-2"></i>Đặt lại form
                                        </button>
                                        <button type="button" class="btn btn-outline-info btn-lg" onclick="previewDiscount()">
                                            <i class="fas fa-eye me-2"></i>Xem trước
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<!-- SweetAlert2 -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<!-- Custom JS -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('discountForm');
    const typeSelect = document.getElementById('type');
    const amountInput = document.getElementById('amount');
    const percentageInput = document.getElementById('percentage');
    const maximumAmountInput = document.getElementById('maximumAmount');
    const startDateInput = document.getElementById('startDate');
    const endDateInput = document.getElementById('endDate');

    // Groups
    const amountGroup = document.getElementById('amountGroup');
    const percentageGroup = document.getElementById('percentageGroup');
    const maximumAmountGroup = document.getElementById('maximumAmountGroup');

    // Xử lý khi thay đổi loại giảm giá
    typeSelect.addEventListener('change', function() {
        const selectedType = this.value;

        // Reset tất cả
        amountGroup.style.display = 'none';
        percentageGroup.style.display = 'none';
        maximumAmountGroup.style.display = 'none';
        amountInput.value = '';
        percentageInput.value = '';
        maximumAmountInput.value = '';

        // Hiển thị field phù hợp
        if (selectedType === '1' || selectedType === '3') {
            // Loại 1 và 3: Giảm theo phần trăm
            percentageGroup.style.display = 'block';
            maximumAmountGroup.style.display = 'block';
            percentageInput.required = true;
            amountInput.required = false;
        } else if (selectedType === '2') {
            // Loại 2: Giảm số tiền cố định
            amountGroup.style.display = 'block';
            amountInput.required = true;
            percentageInput.required = false;
        }
    });

    // Validation form
    form.addEventListener('submit', function(e) {
        const selectedType = typeSelect.value;
        const amount = parseFloat(amountInput.value) || 0;
        const percentage = parseInt(percentageInput.value) || 0;
        const startDate = new Date(startDateInput.value);
        const endDate = new Date(endDateInput.value);

        // Kiểm tra loại giảm giá
        if (!selectedType) {
            e.preventDefault();
            Swal.fire({
                icon: 'error',
                title: 'Lỗi!',
                text: 'Vui lòng chọn loại giảm giá!'
            });
            return false;
        }

        // Kiểm tra giá trị giảm
        if (selectedType === '2' && amount <= 0) {
            e.preventDefault();
            Swal.fire({
                icon: 'error',
                title: 'Lỗi!',
                text: 'Vui lòng nhập số tiền giảm hợp lệ!'
            });
            return false;
        }

        if ((selectedType === '1' || selectedType === '3') && (percentage <= 0 || percentage > 100)) {
            e.preventDefault();
            Swal.fire({
                icon: 'error',
                title: 'Lỗi!',
                text: 'Vui lòng nhập phần trăm giảm từ 1% đến 100%!'
            });
            return false;
        }

        // Kiểm tra ngày
        if (endDate <= startDate) {
            e.preventDefault();
            Swal.fire({
                icon: 'error',
                title: 'Lỗi!',
                text: 'Ngày kết thúc phải sau ngày bắt đầu!'
            });
            return false;
        }

        // Kiểm tra ngày bắt đầu không được trong quá khứ
        const now = new Date();
        if (startDate < now) {
            e.preventDefault();
            Swal.fire({
                icon: 'warning',
                title: 'Cảnh báo!',
                text: 'Ngày bắt đầu không nên trong quá khứ!'
            });
            return false;
        }
    });

    // Set default dates
    const now = new Date();
    now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
    startDateInput.value = now.toISOString().slice(0, 16);

    const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
    endDateInput.value = nextWeek.toISOString().slice(0, 16);
});

// Hàm xem trước mã giảm giá
function previewDiscount() {
    const code = document.getElementById('code').value;
    const detail = document.getElementById('detail').value;
    const type = document.getElementById('type').value;
    const amount = document.getElementById('amount').value;
    const percentage = document.getElementById('percentage').value;
    const maximumAmount = document.getElementById('maximumAmount').value;
    const minimumAmount = document.getElementById('minimumAmountInCart').value;
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;

    if (!code || !type) {
        Swal.fire({
            icon: 'warning',
            title: 'Thiếu thông tin!',
            text: 'Vui lòng nhập ít nhất mã giảm giá và loại giảm giá!'
        });
        return;
    }

    let typeText = '';
    let valueText = '';

    switch(type) {
        case '1':
            typeText = 'Giảm theo phần trăm';
            valueText = percentage ? `${percentage}%` : 'Chưa nhập';
            if (maximumAmount) valueText += ` (tối đa ${parseInt(maximumAmount).toLocaleString('vi-VN')}đ)`;
            break;
        case '2':
            typeText = 'Giảm số tiền cố định';
            valueText = amount ? `${parseInt(amount).toLocaleString('vi-VN')}đ` : 'Chưa nhập';
            break;
        case '3':
            typeText = 'Giảm phần trăm đặc biệt';
            valueText = percentage ? `${percentage}%` : 'Chưa nhập';
            if (maximumAmount) valueText += ` (tối đa ${parseInt(maximumAmount).toLocaleString('vi-VN')}đ)`;
            break;
    }

    const minAmountText = minimumAmount ? `${parseInt(minimumAmount).toLocaleString('vi-VN')}đ` : 'Không giới hạn';
    const startDateText = startDate ? new Date(startDate).toLocaleString('vi-VN') : 'Chưa chọn';
    const endDateText = endDate ? new Date(endDate).toLocaleString('vi-VN') : 'Chưa chọn';

    Swal.fire({
        title: 'Xem trước mã giảm giá',
        html: `
            <div class="text-start">
                <p><strong>Mã:</strong> ${code}</p>
                <p><strong>Mô tả:</strong> ${detail || 'Không có'}</p>
                <p><strong>Loại:</strong> ${typeText}</p>
                <p><strong>Giá trị:</strong> ${valueText}</p>
                <p><strong>Đơn hàng tối thiểu:</strong> ${minAmountText}</p>
                <p><strong>Thời gian:</strong> ${startDateText} - ${endDateText}</p>
            </div>
        `,
        icon: 'info',
        confirmButtonText: 'Đóng'
    });
}
</script>
</body>
</html>
